import { defineComponent } from 'vue';
import HierarchyItem from './hierarchy-item';

const HierarchyView = defineComponent({
  name: 'HierarchyView',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  render() {
    return (
      <div>
        {this.list.map((item) => {
          return <HierarchyItem {...this.$scopedSlots} />;
        })}
      </div>
    );
  },
});

export default HierarchyView;
