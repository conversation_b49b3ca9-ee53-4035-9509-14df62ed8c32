import { defineComponent } from 'vue';
import HierarchyItem from './hierarchy-item';

const HierarchyView = defineComponent({
  name: 'HierarchyView',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    // 是否默认展开所有项
    defaultExpandAll: {
      type: Boolean,
      default: true,
    },
    // 是否可折叠
    collapsible: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['expand', 'collapse'],
  methods: {
    handleExpand(data: any) {
      this.$emit('expand', data);
    },
    handleCollapse(data: any) {
      this.$emit('collapse', data);
    },
  },
  render() {
    const { list, defaultExpandAll, collapsible } = this;

    return (
      <div>
        {(list as any[]).map((item: any, index: number) => {
          return (
            <HierarchyItem
              key={item.id || index}
              data={item}
              defaultExpanded={defaultExpandAll}
              collapsible={collapsible}
              onExpand={this.handleExpand}
              onCollapse={this.handleCollapse}
              scopedSlots={this.$scopedSlots}
            />
          );
        })}
      </div>
    );
  },
});

export default HierarchyView;
