import QIcon from '@/components/global/q-icon';
import { defineComponent } from 'vue';
import styles from './hierarchy-item.module.less';

const HierarchyItem = defineComponent({
  name: 'HierarchyItem',
  props: {
    selectable: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  render() {
    const { header, default: body } = this.$slots;
    return (
      <div class={styles.container}>
        <div class={styles.header}>
          <QIcon type="icon-a-xianduanxia" />
          {header}
        </div>
        <div class={styles.body}>{body}</div>
      </div>
    );
  },
});

export default HierarchyItem;
