import QIcon from '@/components/global/q-icon';
import { defineComponent, ref } from 'vue';
import styles from './hierarchy-item.module.less';

const HierarchyItem = defineComponent({
  name: 'HierarchyItem',
  props: {
    selectable: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    // 是否默认展开
    defaultExpanded: {
      type: Boolean,
      default: true,
    },
    // 是否可折叠
    collapsible: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['expand', 'collapse'],
  setup(props, { emit }) {
    const expanded = ref(props.defaultExpanded);

    const toggleExpanded = () => {
      if (!props.collapsible) return;

      expanded.value = !expanded.value;

      if (expanded.value) {
        emit('expand', props.data);
      } else {
        emit('collapse', props.data);
      }
    };

    return {
      expanded,
      toggleExpanded,
    };
  },
  render() {
    const { header, default: body } = this.$slots;
    const { expanded, toggleExpanded, collapsible } = this;

    return (
      <div class={styles.container}>
        <div class={[styles.header, { [styles.clickable]: collapsible }]} onClick={toggleExpanded}>
          {collapsible && <QIcon type={expanded ? 'icon-a-xianduanshang' : 'icon-a-xianduanxia'} class={styles.expandIcon} />}
          <div class={styles.headerContent}>{header}</div>
        </div>
        <div class={[styles.body, { [styles.collapsed]: !expanded }]}>{body}</div>
      </div>
    );
  },
});

export default HierarchyItem;
