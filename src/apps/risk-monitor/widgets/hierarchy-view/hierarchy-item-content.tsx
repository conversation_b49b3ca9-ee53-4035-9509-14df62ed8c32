import { defineComponent } from 'vue';
import styles from './hierarchy-item.module.less';

const HierarchyItemContent = defineComponent({
  name: 'HierarchyItemContent',
  props: {
    // 内容类型，用于应用不同的样式
    type: {
      type: String,
      default: 'default',
      validator: (value: string) => ['default', 'primary', 'secondary', 'info'].includes(value),
    },
    // 是否有分割线
    divider: {
      type: Boolean,
      default: false,
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false,
    },
    // 自定义样式类名
    className: {
      type: String,
      default: '',
    },
  },
  emits: ['click'],
  methods: {
    handleClick(event: Event) {
      if (this.clickable) {
        this.$emit('click', event);
      }
    },
  },
  render() {
    const { type, divider, clickable, className } = this;
    
    return (
      <div
        class={[
          styles.itemContent,
          styles[`itemContent--${type}`],
          {
            [styles['itemContent--divider']]: divider,
            [styles['itemContent--clickable']]: clickable,
          },
          className,
        ]}
        onClick={this.handleClick}
      >
        {this.$slots.default}
      </div>
    );
  },
});

export default HierarchyItemContent;
