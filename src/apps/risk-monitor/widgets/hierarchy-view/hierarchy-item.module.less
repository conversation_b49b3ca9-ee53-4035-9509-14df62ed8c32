.container {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
  margin-bottom: 8px;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 500;
  color: #333;

  &.clickable {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f0f0f0;
    }
  }
}

.expandIcon {
  margin-right: 8px;
  font-size: 12px;
  color: #666;
  transition: transform 0.2s ease, color 0.2s ease;

  &:hover {
    color: #1890ff;
  }
}

.headerContent {
  flex: 1;
  display: flex;
  align-items: center;
}

.body {
  transition: all 0.3s ease;
  overflow: hidden;

  &.collapsed {
    max-height: 0;
    opacity: 0;
  }

  // 当 body 直接包含内容时的默认样式
  &:not(.collapsed) {
    padding: 16px;
  }

  // 当 body 包含 HierarchyItemContent 组件时，移除默认 padding
  &:not(.collapsed):has(.itemContent) {
    padding: 0;
  }
}

// HierarchyItemContent 组件样式
.itemContent {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  // 不同类型的样式
  &--default {
    background-color: #fff;
    color: #333;
  }

  &--primary {
    background-color: #f6ffed;
    color: #52c41a;
    border-left: 3px solid #52c41a;
  }

  &--secondary {
    background-color: #f0f5ff;
    color: #1890ff;
    border-left: 3px solid #1890ff;
  }

  &--info {
    background-color: #fffbe6;
    color: #faad14;
    border-left: 3px solid #faad14;
  }

  // 分割线样式
  &--divider {
    border-bottom: 2px solid #e8e8e8;
    margin-bottom: 8px;
    padding-bottom: 16px;

    &:last-child {
      border-bottom: 2px solid #e8e8e8;
    }
  }

  // 可点击样式
  &--clickable {
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;
      transform: translateX(2px);
    }

    &.itemContent--primary:hover {
      background-color: #f0f9e6;
    }

    &.itemContent--secondary:hover {
      background-color: #e6f7ff;
    }

    &.itemContent--info:hover {
      background-color: #fff7e6;
    }
  }
}
