.container {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fff;
  margin-bottom: 8px;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 500;
  color: #333;

  &.clickable {
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f0f0f0;
    }
  }
}

.expandIcon {
  margin-right: 8px;
  font-size: 12px;
  color: #666;
  transition: transform 0.2s ease, color 0.2s ease;

  &:hover {
    color: #1890ff;
  }
}

.headerContent {
  flex: 1;
  display: flex;
  align-items: center;
}

.body {
  padding: 16px;
  transition: all 0.3s ease;
  overflow: hidden;

  &.collapsed {
    padding: 0 16px;
    max-height: 0;
    opacity: 0;
  }
}
